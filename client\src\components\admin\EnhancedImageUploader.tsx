import { useState, useRef, ChangeEvent, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Upload, Image, X, Loader2, Check } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface UploadedImage {
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
}

interface EnhancedImageUploaderProps {
  initialUrl?: string;
  onImageUploaded: (url: string) => void;
  label?: string;
}

export default function EnhancedImageUploader({ 
  initialUrl, 
  onImageUploaded,
  label = "Product Image" 
}: EnhancedImageUploaderProps) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl || "");
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string>(initialUrl || "");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Update imageUrl when initialUrl prop changes
  useEffect(() => {
    setImageUrl(initialUrl || "");
    setSelectedImageUrl(initialUrl || "");
  }, [initialUrl]);

  // Fetch uploaded images
  const fetchUploadedImages = async () => {
    setIsLoadingImages(true);
    try {
      const response = await fetch('/api/upload/images', {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch images');
      }
      
      const data = await response.json();
      setUploadedImages(data.images || []);
    } catch (error) {
      console.error('Error fetching images:', error);
      toast({
        title: "Failed to load images",
        description: "Could not load existing images",
        variant: "destructive"
      });
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Load images when component mounts
  useEffect(() => {
    fetchUploadedImages();
  }, []);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const uploadFile = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      setImageUrl(data.url);
      setSelectedImageUrl(data.url);
      onImageUploaded(data.url);

      // Refresh the uploaded images list
      await fetchUploadedImages();

      toast({
        title: "Image uploaded",
        description: "The image was uploaded successfully"
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setImageUrl("");
    setSelectedImageUrl("");
    onImageUploaded("");
  };

  const handleSelectExistingImage = (url: string) => {
    setImageUrl(url);
    setSelectedImageUrl(url);
    onImageUploaded(url);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="product-image">{label}</Label>

      {!imageUrl ? (
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload New</TabsTrigger>
            <TabsTrigger value="existing">Select Existing</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="space-y-4">
            <div
              className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors ${
                isDragging ? "border-primary bg-primary/5" : "border-gray-300 hover:border-primary/50"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                id="product-image"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
              
              <div className="flex flex-col items-center justify-center space-y-2">
                {isUploading ? (
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                ) : (
                  <Upload className="h-8 w-8 text-gray-400" />
                )}
                <div className="text-sm text-gray-600">
                  {isUploading ? (
                    <p>Uploading...</p>
                  ) : (
                    <>
                      <p className="font-medium">Click to upload or drag and drop</p>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="existing" className="space-y-4">
            {isLoadingImages ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading images...</span>
              </div>
            ) : uploadedImages.length === 0 ? (
              <div className="text-center p-8 text-gray-500">
                <Image className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No images uploaded yet</p>
                <p className="text-sm">Upload your first image to get started</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                {uploadedImages.map((image) => (
                  <div
                    key={image.filename}
                    className={`relative group cursor-pointer border-2 rounded-lg overflow-hidden transition-all ${
                      selectedImageUrl === image.url 
                        ? "border-primary ring-2 ring-primary/20" 
                        : "border-gray-200 hover:border-primary/50"
                    }`}
                    onClick={() => handleSelectExistingImage(image.url)}
                  >
                    <div className="aspect-square">
                      <img
                        src={image.url}
                        alt={image.filename}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {selectedImageUrl === image.url && (
                      <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                        <Check className="h-3 w-3" />
                      </div>
                    )}
                    
                    <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                      <p className="truncate">{image.filename}</p>
                      <p>{formatFileSize(image.size)} • {formatDate(image.uploadedAt)}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      ) : (
        <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-md border">
          <img
            src={imageUrl}
            alt="Selected"
            className="h-full w-full object-cover"
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-7 w-7 rounded-full opacity-90"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
