// Homepage configuration interfaces and storage
export interface HeroSection {
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  textColor: string;
  showVideo: boolean;
  videoUrl: string;
}

export interface FeatureItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  enabled: boolean;
}

export interface FeaturesSection {
  title: string;
  subtitle: string;
  features: FeatureItem[];
  layout: 'grid' | 'list' | 'carousel';
  columns: number;
}

export interface TestimonialItem {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating: number;
  enabled: boolean;
}

export interface TestimonialsSection {
  title: string;
  subtitle: string;
  testimonials: TestimonialItem[];
  layout: 'carousel' | 'grid';
  showRatings: boolean;
}

export interface ProductsSection {
  title: string;
  subtitle: string;
  showAllProducts: boolean;
  featuredProductIds: number[];
  layout: 'grid' | 'carousel';
  columns: number;
  showPrices: boolean;
  showDescriptions: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  enabled: boolean;
}

export interface FAQSection {
  title: string;
  subtitle: string;
  faqs: FAQItem[];
  layout: 'accordion' | 'tabs';
}

export interface CTASection {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  backgroundImage: string;
  textColor: string;
}

export interface PageSection {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'products' | 'faq' | 'cta';
  title: string;
  enabled: boolean;
  order: number;
  content: HeroSection | FeaturesSection | TestimonialsSection | ProductsSection | FAQSection | CTASection;
}

export interface SEOSettings {
  title: string;
  description: string;
  keywords: string;
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
  twitterTitle: string;
  twitterDescription: string;
  twitterImage: string;
}

export interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  borderRadius: string;
  spacing: string;
}

export interface HomepageConfig {
  sections: PageSection[];
  seo: SEOSettings;
  theme: ThemeSettings;
  lastUpdated: string;
  version: number;
}

// Default homepage configuration - Empty by default, users will customize
export const defaultHomepageConfig: HomepageConfig = {
  sections: [],
  seo: {
    title: '',
    description: '',
    keywords: '',
    ogTitle: '',
    ogDescription: '',
    ogImage: '',
    twitterTitle: '',
    twitterDescription: '',
    twitterImage: ''
  },
  theme: {
    primaryColor: '#6366f1',
    secondaryColor: '#4f46e5',
    accentColor: '#8b5cf6',
    backgroundColor: '#ffffff',
    textColor: '#1e293b',
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '8px',
    spacing: '1rem'
  },
  lastUpdated: new Date().toISOString(),
  version: 1
};
      id: 'features-1',
      type: 'features',
      title: 'Features Section',
      enabled: true,
      order: 2,
      content: {
        title: 'Why Choose Our Design System',
        subtitle: 'Everything you need to build exceptional productivity apps',
        features: [
          {
            id: 'feature-1',
            icon: '🎨',
            title: 'Premium UI Components',
            description: 'Access hundreds of carefully crafted components designed for productivity applications.',
            enabled: true
          },
          {
            id: 'feature-2',
            icon: '📱',
            title: 'Responsive Templates',
            description: 'All templates are fully responsive and optimized for desktop, tablet, and mobile devices.',
            enabled: true
          },
          {
            id: 'feature-3',
            icon: '⚡',
            title: 'Lightning Fast Setup',
            description: 'Get your productivity app up and running in minutes with our ready-to-use templates.',
            enabled: true
          },
          {
            id: 'feature-4',
            icon: '🔧',
            title: 'Customizable Design System',
            description: 'Easily customize colors, typography, and spacing to match your brand identity.',
            enabled: true
          },
          {
            id: 'feature-5',
            icon: '📚',
            title: 'Comprehensive Documentation',
            description: 'Detailed guides and documentation to help you implement and customize every component.',
            enabled: true
          },
          {
            id: 'feature-6',
            icon: '🚀',
            title: 'Modern Tech Stack',
            description: 'Built with the latest technologies including React, TypeScript, and Tailwind CSS.',
            enabled: true
          }
        ],
        layout: 'grid',
        columns: 3
      } as FeaturesSection
    },
    {
      id: 'products-1',
      type: 'products',
      title: 'Products Section',
      enabled: true,
      order: 3,
      content: {
        title: 'Premium Template Collection',
        subtitle: 'Choose from our curated selection of productivity app templates and UI kits',
        showAllProducts: true,
        featuredProductIds: [],
        layout: 'grid',
        columns: 3,
        showPrices: true,
        showDescriptions: true
      } as ProductsSection
    },
    {
      id: 'cta-1',
      type: 'cta',
      title: 'Call to Action',
      enabled: true,
      order: 4,
      content: {
        title: 'Ready to Build Amazing Apps?',
        description: 'Join thousands of designers and developers already using our templates to create exceptional productivity applications.',
        primaryButtonText: 'Get Templates',
        primaryButtonLink: '#products',
        secondaryButtonText: 'View Demos',
        secondaryButtonLink: '#features',
        backgroundType: 'solid',
        backgroundColor: '#f8fafc',
        backgroundImage: '',
        textColor: '#1e293b'
      } as CTASection
    }
  ],
  seo: {
    title: 'Productivity App Templates & UI/UX Design Systems',
    description: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
    keywords: 'productivity app templates, ui design system, app ui kit, dashboard templates, react components, design system, ui components',
    ogTitle: 'Productivity App Templates & UI/UX Design Systems',
    ogDescription: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
    ogImage: '',
    twitterTitle: 'Productivity App Templates & Design Systems',
    twitterDescription: 'Premium collection of productivity app templates and UI components for modern applications.',
    twitterImage: ''
  },
  theme: {
    primaryColor: '#6366f1',
    secondaryColor: '#4f46e5',
    accentColor: '#8b5cf6',
    backgroundColor: '#ffffff',
    textColor: '#1e293b',
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '8px',
    spacing: '1rem'
  },
  lastUpdated: new Date().toISOString(),
  version: 1
};

// In-memory storage for homepage configuration
export let homepageConfigStorage: HomepageConfig = { ...defaultHomepageConfig };

/**
 * Get the homepage configuration
 */
export function getHomepageConfig(): HomepageConfig {
  return homepageConfigStorage;
}

/**
 * Update the homepage configuration
 */
export function updateHomepageConfig(config: Partial<HomepageConfig>): HomepageConfig {
  homepageConfigStorage = {
    ...homepageConfigStorage,
    ...config,
    lastUpdated: new Date().toISOString(),
    version: homepageConfigStorage.version + 1
  };
  return homepageConfigStorage;
}

/**
 * Update a specific section
 */
export function updateSection(sectionId: string, updates: Partial<PageSection>): HomepageConfig {
  const sectionIndex = homepageConfigStorage.sections.findIndex(s => s.id === sectionId);
  if (sectionIndex !== -1) {
    homepageConfigStorage.sections[sectionIndex] = {
      ...homepageConfigStorage.sections[sectionIndex],
      ...updates
    };
    homepageConfigStorage.lastUpdated = new Date().toISOString();
    homepageConfigStorage.version += 1;
  }
  return homepageConfigStorage;
}

/**
 * Add a new section
 */
export function addSection(section: PageSection): HomepageConfig {
  homepageConfigStorage.sections.push(section);
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Remove a section
 */
export function removeSection(sectionId: string): HomepageConfig {
  homepageConfigStorage.sections = homepageConfigStorage.sections.filter(s => s.id !== sectionId);
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Reorder sections
 */
export function reorderSections(sectionIds: string[]): HomepageConfig {
  const reorderedSections = sectionIds.map((id, index) => {
    const section = homepageConfigStorage.sections.find(s => s.id === id);
    if (section) {
      return { ...section, order: index + 1 };
    }
    return null;
  }).filter(Boolean) as PageSection[];

  homepageConfigStorage.sections = reorderedSections;
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Reset to default configuration
 */
export function resetHomepageConfig(): HomepageConfig {
  homepageConfigStorage = { ...defaultHomepageConfig };
  return homepageConfigStorage;
}
