/**
 * Email Templates Configuration
 * This file defines the structure and default values for email templates
 * that can be customized through the admin interface.
 */

export interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  category: string;
  isDefault: boolean;
}

export interface EmailTemplateCategory {
  id: string;
  name: string;
  description: string;
}

export const EMAIL_TEMPLATE_CATEGORIES: EmailTemplateCategory[] = [
  {
    id: 'purchase',
    name: 'Purchase Emails',
    description: 'Emails sent when a customer makes a purchase'
  },
  {
    id: 'trial',
    name: 'Trial Emails',
    description: 'Emails sent for trial subscriptions'
  },
  {
    id: 'iptv',
    name: 'IPTV & Streaming',
    description: 'Emails for IPTV subscriptions and streaming services'
  },
  {
    id: 'notification',
    name: 'Notification Emails',
    description: 'General notification emails'
  },
  {
    id: 'marketing',
    name: 'Marketing Emails',
    description: 'Emails for marketing campaigns'
  },
  {
    id: 'support',
    name: 'Support Emails',
    description: 'Emails for customer support'
  }
];

// Default email templates - Empty by default, users will create their own
export const DEFAULT_EMAIL_TEMPLATES: EmailTemplate[] = [];

