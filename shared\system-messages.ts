/**
 * System Messages Configuration
 * This file defines the structure and default values for system messages
 * that can be customized through the admin interface.
 */

export interface SystemMessage {
  id: string;
  category: string;
  name: string;
  description: string;
  content: string;
  isHtml: boolean;
}

export interface SystemMessageCategory {
  id: string;
  name: string;
  description: string;
}

export const SYSTEM_MESSAGE_CATEGORIES: SystemMessageCategory[] = [
  {
    id: 'checkout',
    name: 'Checkout Messages',
    description: 'Messages displayed during the checkout process'
  },
  {
    id: 'validation',
    name: 'Validation Messages',
    description: 'Messages displayed when validating user input'
  },
  {
    id: 'error',
    name: 'Error Messages',
    description: 'Messages displayed when errors occur'
  },
  {
    id: 'notification',
    name: 'Notification Messages',
    description: 'General notification messages'
  },
  {
    id: 'email',
    name: 'Email Messages',
    description: 'Messages used in email templates'
  }
];

// Default system messages
export const DEFAULT_SYSTEM_MESSAGES: SystemMessage[] = [];
